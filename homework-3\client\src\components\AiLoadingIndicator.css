.ai-loading-indicator {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
  min-width: 280px;
  animation: slideInFromTop 0.3s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ai-loading-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ai-loading-spinner {
  position: relative;
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: #3b82f6;
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  border-right-color: #10b981;
  animation-delay: 0.3s;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: #f59e0b;
  animation-delay: 0.6s;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.ai-loading-text {
  flex: 1;
  min-width: 0;
}

.ai-loading-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  line-height: 1.2;
}

.ai-loading-progress {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.3;
  word-wrap: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-loading-indicator {
    top: 10px;
    left: 10px;
    right: 10px;
    min-width: auto;
    padding: 12px 16px;
  }
  
  .ai-loading-content {
    gap: 10px;
  }
  
  .ai-loading-spinner {
    width: 28px;
    height: 28px;
  }
  
  .ai-loading-title {
    font-size: 13px;
  }
  
  .ai-loading-progress {
    font-size: 11px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .ai-loading-indicator {
    background: rgba(31, 41, 55, 0.95);
    border-color: #374151;
  }
  
  .ai-loading-title {
    color: #f9fafb;
  }
  
  .ai-loading-progress {
    color: #d1d5db;
  }
}
