import React, { useEffect, useRef } from "react";
import { ChevronRight } from "lucide-react";
import "./ContextMenu.css";

import addSon from "../icon/add/addSon.png";
import addBrother_icon from "../icon/start/brothers.png";
import addParents_icon from "../icon/start/parent.png";

import { add_icons } from "../config/add_menu";
import Ai_icon from "../icon/add/ai.png";
import { generateAiMindMapNodes } from "../utils/aiClient";
import { useMindMapStore } from "../store/mindMapStore";

interface ContextMenuProps {
  isOpen: boolean;
  position: { x: number; y: number };
  nodeId?: string;
  nodeLevel?: number;
  onClose: () => void;
  onAddChild: (nodeId: string) => void;
  onAddSibling: (nodeId: string) => void;
  onDeleteNode: (nodeId: string) => void;
}

interface MenuItemProps {
  icon: React.ReactNode;
  label: string;
  describe?: string;
  onClick: () => void;
  disabled?: boolean;
}

const MenuItem = ({
  icon,
  label,
  describe,
  onClick,
  disabled = false,
}: MenuItemProps) => (
  <button
    type="button"
    className="menu-item"
    onClick={disabled ? undefined : onClick}
    disabled={disabled}
  >
    <span className="menu-item-icon">{icon}</span>
    <span className="menu-item-label">{label}</span>
    {describe !== undefined && (
      <div className="menu-item-describe">
        {describe === "" ? <ChevronRight className="icon-small" /> : describe}
      </div>
    )}
  </button>
);

const MenuSeparator = () => <div className="menu-separator" />;

const ContextMenu = ({
  isOpen,
  position,
  nodeId,
  nodeLevel,
  onClose,
  onAddChild,
  onAddSibling,
  onDeleteNode,
}: ContextMenuProps) => {
  const menuRef = useRef<HTMLDivElement>(null);

  // 使用全局状态获取AI prompt文本和节点操作方法
  const { aiPromptText, addAiGeneratedNode, setAiGenerating } =
    useMindMapStore();

  // 定义generateAi函数，调用AI接口生成思维导图节点
  const generateAi = async () => {
    console.log(" 点击AI创作按钮，开始调用AI接口...");

    // 使用全局状态中的aiPromptText，如果为空则提示用户
    const prompt = aiPromptText.trim();
    if (!prompt) {
      console.warn("⚠️ 请先在节点中输入内容作为AI生成的主题");
      alert("请先在节点中输入内容作为AI生成的主题");
      return;
    }

    // 确保有nodeId（当前右键点击的节点）
    if (!nodeId) {
      console.error("❌ 没有选中的节点");
      return;
    }

    console.log("📝 使用的prompt:", prompt);
    console.log("🎯 目标父节点ID:", nodeId);

    try {
      // 开始AI生成，显示加载状态
      setAiGenerating(true);

      // 调用AI生成思维导图节点
      await generateAiMindMapNodes(prompt, nodeId, {
        onStart: () => {
          console.log("🚀 开始AI思维导图生成...");
        },

        onNodeGenerated: (node) => {
          console.log("📦 生成新节点:", node);
          // 立即添加到思维导图中，实现流式渲染
          addAiGeneratedNode(node);
        },

        onEnd: (allNodes) => {
          console.log(
            "✅ AI思维导图生成完成，共生成",
            allNodes.length,
            "个节点"
          );
          // 隐藏加载状态
          setAiGenerating(false);
        },

        onError: (error) => {
          console.error("❌ AI生成错误:", error);
          setAiGenerating(false);
          alert(`AI生成失败: ${error}`);
        },
      });

      // 关闭右键菜单
      onClose();
    } catch (error) {
      console.error("❌ AI创作失败:", error);
      setAiGenerating(false);
      alert(
        `AI创作失败: ${error instanceof Error ? error.message : "未知错误"}`
      );
    }
  };

  // 处理点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  // 计算菜单位置，防止超出屏幕
  const getMenuPosition = () => {
    const menuWidth = 180;
    const menuHeight = 200; // 估算高度
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    let x = position.x;
    let y = position.y;

    // 防止超出右边界
    if (x + menuWidth > screenWidth) {
      x = screenWidth - menuWidth - 10;
    }

    // 防止超出底边界
    if (y + menuHeight > screenHeight) {
      y = screenHeight - menuHeight - 10;
    }

    // 防止超出左边界
    if (x < 0) {
      x = 10;
    }

    // 防止超出顶边界
    if (y < 0) {
      y = 10;
    }

    return { x, y };
  };

  if (!isOpen || !nodeId || !nodeLevel) return null;

  const menuPosition = getMenuPosition();

  // 处理菜单项点击
  const handleAddChild = () => {
    onAddChild(nodeId);
    onClose();
  };

  const handleAddSibling = () => {
    onAddSibling(nodeId);
    onClose();
  };

  const handleDelete = () => {
    onDeleteNode(nodeId);
    onClose();
  };

  // 根据节点层级渲染不同的菜单项
  const renderMenuItems = () => {
    // 渲染通用菜单项的函数
    const renderCommonMenuItems = () => {
      return add_icons.map(({ icon, label, describe, onClick }, index) => (
        <React.Fragment key={`${label}-${index}`}>
          {/* 如果前一个菜单项有 addMenuSeparator 标记，则添加分隔符 */}
          {index > 0 && add_icons[index - 1].addMenuSeparator && (
            <MenuSeparator />
          )}
          <MenuItem
            icon={
              icon ? (
                <img src={icon} alt={label} className="icon-small" />
              ) : null
            }
            label={label}
            describe={describe}
            onClick={() => {
              // 如果nodeLevel不是1，并且label是'删除'，则使用handleDelete
              if (nodeLevel !== 1 && label === "删除") {
                handleDelete();
              } else {
                onClick();
              }
              onClose();
            }}
            disabled={(() => {
              // 根据节点级别和标签设置禁用状态
              if (nodeLevel === 1) {
                const level1DisabledLabels = [
                  "收起主题",
                  "选择主题",
                  "剪切",
                  "删除",
                  "删除当前主题",
                  "聚焦模式",
                ];
                return level1DisabledLabels.includes(label);
              }
              if (nodeLevel === 3) {
                const level3DisabledLabels = ["收起主题", "删除当前主题"];
                return level3DisabledLabels.includes(label);
              }
              return false; // 其他情况不禁用
            })()}
          />
        </React.Fragment>
      ));
    };

    switch (nodeLevel) {
      case 1: // 根节点菜单
        return (
          <>
            <MenuItem
              icon={<img src={Ai_icon} alt="AI创作" className="icon-small" />}
              label="AI创作"
              describe=""
              onClick={generateAi}
            />
            <MenuItem
              icon={
                <img src={addSon} alt="新增子主题" className="icon-small" />
              }
              label="新增子主题"
              describe="Tab"
              onClick={handleAddChild}
            />
            <MenuItem
              icon={
                <img
                  src={addBrother_icon}
                  alt="新增同级主题"
                  className="icon-small"
                />
              }
              label="新增同级主题"
              describe="Enter"
              onClick={() => onClose()}
              disabled
            />
            <MenuItem
              icon={
                <img
                  src={addParents_icon}
                  alt="新增父主题"
                  className="icon-small"
                />
              }
              label="新增父主题"
              describe="Shift + Tab"
              onClick={() => onClose()}
              disabled
            />
            <MenuSeparator />
            {renderCommonMenuItems()}
          </>
        );

      case 2: // 二级节点菜单
        return (
          <>
            <MenuItem
              icon={
                <img src={addSon} alt="新增子主题" className="icon-small" />
              }
              label="新增子主题"
              describe="Tab"
              onClick={handleAddChild}
            />
            <MenuItem
              icon={
                <img
                  src={addBrother_icon}
                  alt="新增同级主题"
                  className="icon-small"
                />
              }
              label="新增同级主题"
              describe="Enter"
              onClick={handleAddSibling}
            />
            <MenuItem
              icon={
                <img
                  src={addParents_icon}
                  alt="新增父主题"
                  className="icon-small"
                />
              }
              label="新增父主题"
              describe="Shift + Tab"
              onClick={() => onClose()}
              disabled
            />
            <MenuSeparator />
            {renderCommonMenuItems()}
          </>
        );

      case 3: // 三级节点菜单
        return (
          <>
            <MenuItem
              icon={
                <img src={addSon} alt="新增子主题" className="icon-small" />
              }
              label="新增子主题"
              describe="Tab"
              onClick={() => onClose()}
              disabled
            />
            <MenuItem
              icon={
                <img
                  src={addBrother_icon}
                  alt="新增同级主题"
                  className="icon-small"
                />
              }
              label="新增同级主题"
              describe="Enter"
              onClick={handleAddSibling}
            />
            <MenuItem
              icon={
                <img
                  src={addParents_icon}
                  alt="新增父主题"
                  className="icon-small"
                />
              }
              label="新增父主题"
              describe="Shift + Tab"
              onClick={() => onClose()}
              disabled
            />
            <MenuSeparator />
            {renderCommonMenuItems()}
          </>
        );

      default:
        return null;
    }
  };

  return (
    <div
      ref={menuRef}
      className="context-menu"
      style={{
        left: menuPosition.x,
        bottom: "5px",
      }}
    >
      {renderMenuItems()}
    </div>
  );
};

export default ContextMenu;
