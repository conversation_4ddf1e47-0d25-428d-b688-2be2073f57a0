/**
 * AI接口测试文件
 * 演示如何使用generateAi函数调用/api/chatStream接口
 */

import { generateAi, generateAiSimple } from '../utils/aiClient';

/**
 * 测试基本的AI调用功能
 */
export const testBasicAI = async () => {
  console.log('=== 测试基本AI调用功能 ===');
  
  const prompt = "请介绍一下React框架的主要特点和优势";
  
  try {
    const result = await generateAi(prompt, {
      onStart: () => {
        console.log('🚀 开始生成AI响应...');
      },
      onData: (text) => {
        console.log('📝 接收到:', text);
      },
      onEnd: (fullResponse) => {
        console.log('✅ 生成完成!');
        console.log('📄 完整响应长度:', fullResponse.length);
      },
      onError: (error) => {
        console.error('❌ 发生错误:', error);
      }
    });
    
    console.log('\n🎉 测试完成，最终结果:');
    console.log(result);
    
  } catch (error) {
    console.error('💥 测试失败:', error);
  }
};

/**
 * 测试简化版AI调用
 */
export const testSimpleAI = async () => {
  console.log('\n=== 测试简化版AI调用 ===');
  
  const prompt = "请用一句话总结JavaScript的特点";
  
  await generateAiSimple(prompt);
};

/**
 * 测试多个不同的prompt
 */
export const testMultiplePrompts = async () => {
  console.log('\n=== 测试多个不同的prompt ===');
  
  const prompts = [
    "什么是TypeScript？",
    "解释一下Vue.js的响应式原理",
    "Node.js的优势有哪些？"
  ];
  
  for (let i = 0; i < prompts.length; i++) {
    console.log(`\n--- 测试 ${i + 1}/${prompts.length}: ${prompts[i]} ---`);
    
    try {
      await generateAiSimple(prompts[i]);
      
      // 在请求之间添加延迟，避免过于频繁的请求
      if (i < prompts.length - 1) {
        console.log('⏳ 等待2秒后继续下一个测试...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } catch (error) {
      console.error(`❌ 测试 ${i + 1} 失败:`, error);
    }
  }
};

/**
 * 测试错误处理
 */
export const testErrorHandling = async () => {
  console.log('\n=== 测试错误处理 ===');
  
  // 测试空prompt
  console.log('📝 测试空prompt...');
  try {
    await generateAi('');
  } catch (error) {
    console.log('✅ 正确捕获空prompt错误:', error);
  }
  
  // 测试只有空格的prompt
  console.log('📝 测试只有空格的prompt...');
  try {
    await generateAi('   ');
  } catch (error) {
    console.log('✅ 正确捕获空格prompt错误:', error);
  }
};

/**
 * 运行所有测试
 */
export const runAllTests = async () => {
  console.log('🧪 开始运行AI接口测试套件...\n');
  
  try {
    await testErrorHandling();
    await testBasicAI();
    await testSimpleAI();
    await testMultiplePrompts();
    
    console.log('\n🎊 所有测试完成!');
  } catch (error) {
    console.error('💥 测试套件执行失败:', error);
  }
};

// 如果直接运行此文件，执行所有测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数挂载到window对象上，方便在控制台调用
  (window as any).aiTest = {
    testBasicAI,
    testSimpleAI,
    testMultiplePrompts,
    testErrorHandling,
    runAllTests
  };
  
  console.log('🔧 AI测试工具已加载到 window.aiTest');
  console.log('📖 可用的测试函数:');
  console.log('  - window.aiTest.testBasicAI()');
  console.log('  - window.aiTest.testSimpleAI()');
  console.log('  - window.aiTest.testMultiplePrompts()');
  console.log('  - window.aiTest.testErrorHandling()');
  console.log('  - window.aiTest.runAllTests()');
}
