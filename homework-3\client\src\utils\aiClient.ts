/**
 * AI客户端工具类
 * 用于调用服务器的AI接口
 */

export interface AIStreamResponse {
  text?: string;
  message?: string;
  error?: string;
}

export interface AIStreamCallbacks {
  onStart?: () => void;
  onData?: (text: string) => void;
  onEnd?: (fullResponse: string) => void;
  onError?: (error: string) => void;
}

/**
 * 调用AI流式接口
 * @param prompt 用户输入的提示词
 * @param callbacks 回调函数
 * @returns Promise<string> 完整的AI响应
 */
export const generateAi = async (
  prompt: string,
  callbacks?: AIStreamCallbacks
): Promise<string> => {
  console.log("开始调用AI生成接口...", { prompt });
  
  if (!prompt || prompt.trim() === '') {
    throw new Error('prompt不能为空');
  }

  try {
    // 调用流式AI接口
    const response = await fetch('/api/chatStream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ prompt: prompt.trim() }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 检查响应是否为SSE格式
    if (!response.body) {
      throw new Error('Response body is null');
    }

    console.log("开始接收SSE流式响应...");
    
    // 创建流读取器
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let fullResponse = '';

    // 读取流数据
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log("流式响应结束");
        break;
      }

      // 解码数据
      buffer += decoder.decode(value, { stream: true });
      
      // 按行分割SSE数据
      const lines = buffer.split('\n\n');
      buffer = lines.pop() || '';
      
      // 处理每一行SSE数据
      for (const line of lines) {
        if (line.trim() === '') continue;
        
        // 解析SSE事件
        const eventMatch = line.match(/^event: (.+)$/m);
        const dataMatch = line.match(/^data: (.+)$/m);
        
        if (eventMatch && dataMatch) {
          const eventType = eventMatch[1];
          const eventData = dataMatch[1];
          
          console.log(`事件类型: ${eventType}, 数据:`, eventData);
          
          switch (eventType) {
            case 'start':
              console.log("AI开始生成响应...");
              callbacks?.onStart?.();
              break;
              
            case 'end':
              console.log("AI响应完成");
              console.log("完整响应内容:", fullResponse);
              callbacks?.onEnd?.(fullResponse);
              break;
              
            case 'error':
              console.error("AI响应错误:", eventData);
              let errorMessage = 'AI响应错误';
              try {
                const parsedError = JSON.parse(eventData);
                errorMessage = parsedError.message || parsedError.error || errorMessage;
              } catch (e) {
                errorMessage = eventData || errorMessage;
              }
              callbacks?.onError?.(errorMessage);
              break;
          }
        } else if (dataMatch) {
          // 处理没有event标签的数据行
          try {
            const parsedData: AIStreamResponse = JSON.parse(dataMatch[1]);
            if (parsedData.text) {
              console.log("接收到文本片段:", parsedData.text);
              fullResponse += parsedData.text;
              callbacks?.onData?.(parsedData.text);
            }
          } catch (e) {
            console.warn('解析数据错误:', e, dataMatch[1]);
          }
        }
      }
    }
    
    console.log("\n=== AI生成完成 ===");
    console.log("最终完整响应:", fullResponse);
    
    return fullResponse;
    
  } catch (error) {
    console.error('调用AI接口时发生错误:', error);
    
    // 如果是网络错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('fetch')) {
      const networkError = '网络连接错误，请检查服务器是否正在运行';
      console.error(networkError);
      callbacks?.onError?.(networkError);
      throw new Error(networkError);
    }
    
    callbacks?.onError?.(error instanceof Error ? error.message : '未知错误');
    throw error;
  }
};

/**
 * 简化版本的generateAi函数，直接打印结果到控制台
 * @param prompt 用户输入的提示词
 */
export const generateAiSimple = async (prompt: string): Promise<void> => {
  try {
    const result = await generateAi(prompt, {
      onStart: () => console.log('🤖 AI开始思考...'),
      onData: (text) => console.log('📝 实时输出:', text),
      onEnd: (fullResponse) => {
        console.log('\n✅ AI响应完成!');
        console.log('📄 完整内容:');
        console.log('=' .repeat(50));
        console.log(fullResponse);
        console.log('=' .repeat(50));
      },
      onError: (error) => console.error('❌ 错误:', error)
    });
  } catch (error) {
    console.error('💥 调用失败:', error);
  }
};

// 导出默认的generateAi函数
export default generateAi;
