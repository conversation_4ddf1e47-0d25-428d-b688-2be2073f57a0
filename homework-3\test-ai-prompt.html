<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Prompt功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #555;
            margin-top: 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        .prompt-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .instructions h4 {
            margin-top: 0;
            color: #0066cc;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 2px 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI Prompt功能测试</h1>
        
        <div class="instructions">
            <h4>📋 功能说明</h4>
            <p>这个测试页面用于验证AI prompt功能的实现：</p>
            <ol>
                <li>在MindMapNode组件中，当用户编辑节点文本时，<span class="code">editText</span>的值会自动同步到全局状态<span class="code">aiPromptText</span></li>
                <li>在ContextMenu组件中，点击"AI创作"按钮时，generateAi函数会使用全局状态中的<span class="code">aiPromptText</span>作为prompt</li>
                <li>如果<span class="code">aiPromptText</span>为空，则使用默认的React介绍prompt</li>
            </ol>
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>确保服务器运行在 http://localhost:3001</li>
                <li>在React应用中双击任意节点进入编辑模式</li>
                <li>输入自定义文本（如"介绍一下Vue.js"）</li>
                <li>右键点击节点，选择"AI创作"</li>
                <li>观察控制台输出，应该看到使用了你输入的文本作为prompt</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🧪 模拟测试</h3>
            <p>在这里可以模拟测试AI prompt功能：</p>
            <textarea class="prompt-input" id="testPrompt" placeholder="输入测试prompt，例如：介绍一下Vue.js的特点" rows="3"></textarea>
            <button onclick="testAiPrompt()">测试AI调用</button>
            <div id="testStatus"></div>
            <div id="testOutput" class="output" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 实现细节</h3>
            <p><strong>修改的文件：</strong></p>
            <ul>
                <li><span class="code">homework-3/client/src/store/mindMapStore.ts</span> - 添加了aiPromptText状态和setAiPromptText方法</li>
                <li><span class="code">homework-3/client/src/components/MindMapNode.tsx</span> - 在input的onChange事件中调用setAiPromptText</li>
                <li><span class="code">homework-3/client/src/components/ContextMenu.tsx</span> - generateAi函数使用全局状态中的aiPromptText</li>
                <li><span class="code">homework-3/client/src/utils/aiClient.ts</span> - 修改API调用地址为localhost:3001</li>
            </ul>
            
            <p><strong>核心逻辑：</strong></p>
            <pre class="code">
// 在MindMapNode.tsx中
onChange={(e) => {
  const newText = e.target.value;
  setEditText(newText);
  setAiPromptText(newText); // 同步到全局状态
  // ...
}}

// 在ContextMenu.tsx中
const generateAi = async () => {
  const prompt = aiPromptText.trim() || "默认prompt";
  await generateAiSimple(prompt);
};
            </pre>
        </div>
    </div>

    <script>
        // 模拟AI调用函数
        async function generateAi(prompt, callbacks = {}) {
            console.log("🚀 开始调用AI生成接口...", { prompt });
            
            if (!prompt || prompt.trim() === '') {
                throw new Error('prompt不能为空');
            }

            try {
                const response = await fetch('http://localhost:3001/api/chatStream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ prompt: prompt.trim() }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                if (!response.body) {
                    throw new Error('Response body is null');
                }

                console.log("📡 开始接收SSE流式响应...");
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let fullResponse = '';

                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        console.log("✅ 流式响应结束");
                        break;
                    }

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n\n');
                    buffer = lines.pop() || '';
                    
                    for (const line of lines) {
                        if (line.trim() === '') continue;
                        
                        const eventMatch = line.match(/^event: (.+)$/m);
                        const dataMatch = line.match(/^data: (.+)$/m);
                        
                        if (eventMatch && dataMatch) {
                            const eventType = eventMatch[1];
                            const eventData = dataMatch[1];
                            
                            console.log(`📨 事件类型: ${eventType}, 数据:`, eventData);
                            
                            switch (eventType) {
                                case 'start':
                                    console.log("🤖 AI开始生成响应...");
                                    callbacks.onStart?.();
                                    break;
                                case 'end':
                                    console.log("🎉 AI响应完成");
                                    console.log("📄 完整响应内容:", fullResponse);
                                    callbacks.onEnd?.(fullResponse);
                                    break;
                                case 'error':
                                    console.error("❌ AI响应错误:", eventData);
                                    let errorMessage = 'AI响应错误';
                                    try {
                                        const parsedError = JSON.parse(eventData);
                                        errorMessage = parsedError.message || parsedError.error || errorMessage;
                                    } catch (e) {
                                        errorMessage = eventData || errorMessage;
                                    }
                                    callbacks.onError?.(errorMessage);
                                    break;
                            }
                        } else if (dataMatch) {
                            try {
                                const parsedData = JSON.parse(dataMatch[1]);
                                if (parsedData.text) {
                                    console.log("📝 接收到文本片段:", parsedData.text);
                                    fullResponse += parsedData.text;
                                    callbacks.onData?.(parsedData.text);
                                }
                            } catch (e) {
                                console.warn('⚠️ 解析数据错误:', e, dataMatch[1]);
                            }
                        }
                    }
                }
                
                console.log("\n🎊 AI生成完成");
                console.log("📋 最终完整响应:", fullResponse);
                
                return fullResponse;
                
            } catch (error) {
                console.error('💥 调用AI接口时发生错误:', error);
                
                if (error instanceof TypeError && error.message.includes('fetch')) {
                    const networkError = '🌐 网络连接错误，请检查服务器是否正在运行';
                    console.error(networkError);
                    callbacks.onError?.(networkError);
                    throw new Error(networkError);
                }
                
                callbacks.onError?.(error instanceof Error ? error.message : '未知错误');
                throw error;
            }
        }

        async function testAiPrompt() {
            const prompt = document.getElementById('testPrompt').value.trim();
            const outputDiv = document.getElementById('testOutput');
            
            if (!prompt) {
                updateStatus('testStatus', '❌ 请输入测试prompt', 'error');
                return;
            }

            updateStatus('testStatus', '🚀 正在测试AI调用...', 'info');
            outputDiv.style.display = 'block';
            outputDiv.textContent = '';

            try {
                const result = await generateAi(prompt, {
                    onStart: () => {
                        outputDiv.textContent += '🤖 AI开始生成响应...\n\n';
                    },
                    onData: (text) => {
                        outputDiv.textContent += text;
                        outputDiv.scrollTop = outputDiv.scrollHeight;
                    },
                    onEnd: (fullResponse) => {
                        outputDiv.textContent += '\n\n✅ 响应完成！';
                        updateStatus('testStatus', '✅ 测试成功！AI使用了自定义prompt。', 'success');
                        
                        console.log('\n' + '='.repeat(50));
                        console.log('🎯 测试结果 - 使用的prompt:', prompt);
                        console.log('📄 AI完整响应:', fullResponse);
                        console.log('='.repeat(50));
                    },
                    onError: (error) => {
                        outputDiv.textContent += `\n\n❌ 错误: ${error}`;
                        updateStatus('testStatus', `❌ 测试失败: ${error}`, 'error');
                    }
                });
            } catch (error) {
                outputDiv.textContent += `\n\n💥 异常: ${error.message}`;
                updateStatus('testStatus', `💥 测试异常: ${error.message}`, 'error');
            }
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 AI Prompt功能测试页面已加载');
            console.log('📖 功能说明：');
            console.log('  1. 在React应用中编辑节点文本时，文本会同步到全局状态aiPromptText');
            console.log('  2. 点击AI创作时，会使用aiPromptText作为prompt调用AI接口');
            console.log('  3. 这个页面可以独立测试AI调用功能');
            
            // 设置默认测试prompt
            document.getElementById('testPrompt').value = '请介绍一下Vue.js框架的主要特点和优势';
        });
    </script>
</body>
</html>
