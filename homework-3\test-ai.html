<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI接口测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #555;
            margin-top: 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .prompt-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .instructions h4 {
            margin-top: 0;
            color: #0066cc;
        }
        .instructions ol {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI接口测试页面</h1>
        
        <div class="instructions">
            <h4>📋 使用说明</h4>
            <ol>
                <li>确保服务器正在运行 (通常在 http://localhost:3000)</li>
                <li>打开浏览器开发者工具的控制台 (F12)</li>
                <li>点击下面的测试按钮</li>
                <li>在控制台中查看详细的AI响应过程</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🚀 快速测试</h3>
            <p>点击按钮测试AI接口的基本功能：</p>
            <button onclick="testBasicAI()">测试基本AI调用</button>
            <button onclick="testSimpleAI()">测试简化版AI调用</button>
            <button onclick="testErrorHandling()">测试错误处理</button>
            <div id="quickTestStatus"></div>
        </div>

        <div class="test-section">
            <h3>✏️ 自定义测试</h3>
            <p>输入自定义prompt测试AI响应：</p>
            <textarea class="prompt-input" id="customPrompt" placeholder="请输入您想要测试的prompt，例如：请介绍一下JavaScript的特点" rows="3"></textarea>
            <button onclick="testCustomPrompt()">发送自定义Prompt</button>
            <div id="customTestStatus"></div>
            <div id="customOutput" class="output" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🧪 批量测试</h3>
            <p>运行多个预设的测试用例：</p>
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="testMultiplePrompts()">测试多个Prompt</button>
            <div id="batchTestStatus"></div>
        </div>

        <div class="test-section">
            <h3>📊 服务器状态检查</h3>
            <p>检查服务器连接状态：</p>
            <button onclick="checkServerStatus()">检查服务器状态</button>
            <div id="serverStatus"></div>
        </div>
    </div>

    <script>
        // AI客户端函数 - 简化版本，用于测试
        async function generateAi(prompt, callbacks = {}) {
            console.log("🚀 开始调用AI生成接口...", { prompt });
            
            if (!prompt || prompt.trim() === '') {
                throw new Error('prompt不能为空');
            }

            try {
                const response = await fetch('/api/chatStream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ prompt: prompt.trim() }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                if (!response.body) {
                    throw new Error('Response body is null');
                }

                console.log("📡 开始接收SSE流式响应...");
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let fullResponse = '';

                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        console.log("✅ 流式响应结束");
                        break;
                    }

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n\n');
                    buffer = lines.pop() || '';
                    
                    for (const line of lines) {
                        if (line.trim() === '') continue;
                        
                        const eventMatch = line.match(/^event: (.+)$/m);
                        const dataMatch = line.match(/^data: (.+)$/m);
                        
                        if (eventMatch && dataMatch) {
                            const eventType = eventMatch[1];
                            const eventData = dataMatch[1];
                            
                            console.log(`📨 事件类型: ${eventType}, 数据:`, eventData);
                            
                            switch (eventType) {
                                case 'start':
                                    console.log("🤖 AI开始生成响应...");
                                    callbacks.onStart?.();
                                    break;
                                case 'end':
                                    console.log("🎉 AI响应完成");
                                    console.log("📄 完整响应内容:", fullResponse);
                                    callbacks.onEnd?.(fullResponse);
                                    break;
                                case 'error':
                                    console.error("❌ AI响应错误:", eventData);
                                    let errorMessage = 'AI响应错误';
                                    try {
                                        const parsedError = JSON.parse(eventData);
                                        errorMessage = parsedError.message || parsedError.error || errorMessage;
                                    } catch (e) {
                                        errorMessage = eventData || errorMessage;
                                    }
                                    callbacks.onError?.(errorMessage);
                                    break;
                            }
                        } else if (dataMatch) {
                            try {
                                const parsedData = JSON.parse(dataMatch[1]);
                                if (parsedData.text) {
                                    console.log("📝 接收到文本片段:", parsedData.text);
                                    fullResponse += parsedData.text;
                                    callbacks.onData?.(parsedData.text);
                                }
                            } catch (e) {
                                console.warn('⚠️ 解析数据错误:', e, dataMatch[1]);
                            }
                        }
                    }
                }
                
                console.log("\n🎊 AI生成完成");
                console.log("📋 最终完整响应:", fullResponse);
                
                return fullResponse;
                
            } catch (error) {
                console.error('💥 调用AI接口时发生错误:', error);
                
                if (error instanceof TypeError && error.message.includes('fetch')) {
                    const networkError = '🌐 网络连接错误，请检查服务器是否正在运行';
                    console.error(networkError);
                    callbacks.onError?.(networkError);
                    throw new Error(networkError);
                }
                
                callbacks.onError?.(error instanceof Error ? error.message : '未知错误');
                throw error;
            }
        }

        // 测试函数
        async function testBasicAI() {
            updateStatus('quickTestStatus', '🚀 开始基本AI测试...', 'info');
            const prompt = "请介绍一下React框架的主要特点和优势";
            
            try {
                const result = await generateAi(prompt, {
                    onStart: () => console.log('🤖 AI开始思考...'),
                    onData: (text) => console.log('📝 实时输出:', text),
                    onEnd: (fullResponse) => {
                        console.log('✅ AI响应完成!');
                        updateStatus('quickTestStatus', '✅ 基本AI测试完成！请查看控制台获取详细信息。', 'success');
                    },
                    onError: (error) => {
                        console.error('❌ 错误:', error);
                        updateStatus('quickTestStatus', `❌ 测试失败: ${error}`, 'error');
                    }
                });
            } catch (error) {
                updateStatus('quickTestStatus', `💥 测试失败: ${error.message}`, 'error');
            }
        }

        async function testSimpleAI() {
            updateStatus('quickTestStatus', '🚀 开始简化版AI测试...', 'info');
            const prompt = "请用一句话总结JavaScript的特点";
            
            try {
                await generateAi(prompt);
                updateStatus('quickTestStatus', '✅ 简化版AI测试完成！请查看控制台获取详细信息。', 'success');
            } catch (error) {
                updateStatus('quickTestStatus', `💥 测试失败: ${error.message}`, 'error');
            }
        }

        async function testErrorHandling() {
            updateStatus('quickTestStatus', '🧪 开始错误处理测试...', 'info');
            
            try {
                await generateAi('');
            } catch (error) {
                console.log('✅ 正确捕获空prompt错误:', error.message);
                updateStatus('quickTestStatus', '✅ 错误处理测试完成！空prompt错误被正确捕获。', 'success');
            }
        }

        async function testCustomPrompt() {
            const prompt = document.getElementById('customPrompt').value.trim();
            const outputDiv = document.getElementById('customOutput');
            
            if (!prompt) {
                updateStatus('customTestStatus', '❌ 请输入prompt', 'error');
                return;
            }

            updateStatus('customTestStatus', '🚀 正在发送自定义prompt...', 'info');
            outputDiv.style.display = 'block';
            outputDiv.textContent = '';

            try {
                const result = await generateAi(prompt, {
                    onStart: () => {
                        outputDiv.textContent += '🤖 AI开始生成响应...\n';
                    },
                    onData: (text) => {
                        outputDiv.textContent += text;
                        outputDiv.scrollTop = outputDiv.scrollHeight;
                    },
                    onEnd: (fullResponse) => {
                        updateStatus('customTestStatus', '✅ 自定义prompt测试完成！', 'success');
                    },
                    onError: (error) => {
                        updateStatus('customTestStatus', `❌ 测试失败: ${error}`, 'error');
                        outputDiv.textContent += `\n❌ 错误: ${error}`;
                    }
                });
            } catch (error) {
                updateStatus('customTestStatus', `💥 测试失败: ${error.message}`, 'error');
                outputDiv.textContent += `\n💥 错误: ${error.message}`;
            }
        }

        async function runAllTests() {
            updateStatus('batchTestStatus', '🧪 开始运行所有测试...', 'info');
            
            try {
                await testErrorHandling();
                await new Promise(resolve => setTimeout(resolve, 1000));
                await testBasicAI();
                await new Promise(resolve => setTimeout(resolve, 2000));
                await testSimpleAI();
                
                updateStatus('batchTestStatus', '🎊 所有测试完成！请查看控制台获取详细信息。', 'success');
            } catch (error) {
                updateStatus('batchTestStatus', `💥 测试套件执行失败: ${error.message}`, 'error');
            }
        }

        async function testMultiplePrompts() {
            updateStatus('batchTestStatus', '🔄 开始多prompt测试...', 'info');
            
            const prompts = [
                "什么是TypeScript？",
                "解释一下Vue.js的响应式原理",
                "Node.js的优势有哪些？"
            ];
            
            for (let i = 0; i < prompts.length; i++) {
                console.log(`\n--- 测试 ${i + 1}/${prompts.length}: ${prompts[i]} ---`);
                
                try {
                    await generateAi(prompts[i]);
                    
                    if (i < prompts.length - 1) {
                        console.log('⏳ 等待2秒后继续下一个测试...');
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }
                } catch (error) {
                    console.error(`❌ 测试 ${i + 1} 失败:`, error);
                }
            }
            
            updateStatus('batchTestStatus', '✅ 多prompt测试完成！请查看控制台获取详细信息。', 'success');
        }

        async function checkServerStatus() {
            updateStatus('serverStatus', '🔍 正在检查服务器状态...', 'info');
            
            try {
                const response = await fetch('/api/chatStream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ prompt: 'test' }),
                });
                
                if (response.ok) {
                    updateStatus('serverStatus', '✅ 服务器连接正常', 'success');
                } else {
                    updateStatus('serverStatus', `⚠️ 服务器响应异常: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('serverStatus', `❌ 无法连接到服务器: ${error.message}`, 'error');
            }
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 AI测试页面已加载');
            console.log('📖 可用的测试函数:');
            console.log('  - testBasicAI()');
            console.log('  - testSimpleAI()');
            console.log('  - testErrorHandling()');
            console.log('  - testCustomPrompt()');
            console.log('  - runAllTests()');
            console.log('  - testMultiplePrompts()');
            console.log('  - checkServerStatus()');
        });
    </script>
</body>
</html>
