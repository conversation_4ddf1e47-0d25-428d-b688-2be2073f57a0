<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>generateAi函数测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        .prompt-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 generateAi函数测试页面</h1>
        
        <div class="instructions">
            <h4>📋 使用说明</h4>
            <p>这个页面用于测试调用 <code>/api/chatStream</code> 接口的 generateAi 函数。</p>
            <ol>
                <li>确保服务器正在运行在 http://localhost:3001</li>
                <li>打开浏览器开发者工具的控制台 (F12)</li>
                <li>点击下面的测试按钮</li>
                <li>观察控制台输出和页面上的实时响应</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🚀 基本测试</h3>
            <button onclick="testBasicAI()">测试React介绍</button>
            <button onclick="testJavaScript()">测试JavaScript特点</button>
            <button onclick="testTypeScript()">测试TypeScript介绍</button>
            <div id="basicStatus"></div>
            <div id="basicOutput" class="output" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>✏️ 自定义测试</h3>
            <textarea class="prompt-input" id="customPrompt" placeholder="请输入您的prompt，例如：请介绍一下Vue.js框架" rows="3"></textarea>
            <button onclick="testCustomPrompt()">发送自定义Prompt</button>
            <div id="customStatus"></div>
            <div id="customOutput" class="output" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔍 服务器连接测试</h3>
            <button onclick="checkConnection()">检查服务器连接</button>
            <div id="connectionStatus"></div>
        </div>
    </div>

    <script>
        // generateAi函数实现
        async function generateAi(prompt, callbacks = {}) {
            console.log("🚀 开始调用AI生成接口...", { prompt });
            
            if (!prompt || prompt.trim() === '') {
                throw new Error('prompt不能为空');
            }

            try {
                const response = await fetch('http://localhost:3001/api/chatStream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ prompt: prompt.trim() }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                if (!response.body) {
                    throw new Error('Response body is null');
                }

                console.log("📡 开始接收SSE流式响应...");
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let fullResponse = '';

                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        console.log("✅ 流式响应结束");
                        break;
                    }

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n\n');
                    buffer = lines.pop() || '';
                    
                    for (const line of lines) {
                        if (line.trim() === '') continue;
                        
                        const eventMatch = line.match(/^event: (.+)$/m);
                        const dataMatch = line.match(/^data: (.+)$/m);
                        
                        if (eventMatch && dataMatch) {
                            const eventType = eventMatch[1];
                            const eventData = dataMatch[1];
                            
                            console.log(`📨 事件类型: ${eventType}, 数据:`, eventData);
                            
                            switch (eventType) {
                                case 'start':
                                    console.log("🤖 AI开始生成响应...");
                                    callbacks.onStart?.();
                                    break;
                                case 'end':
                                    console.log("🎉 AI响应完成");
                                    console.log("📄 完整响应内容:", fullResponse);
                                    callbacks.onEnd?.(fullResponse);
                                    break;
                                case 'error':
                                    console.error("❌ AI响应错误:", eventData);
                                    let errorMessage = 'AI响应错误';
                                    try {
                                        const parsedError = JSON.parse(eventData);
                                        errorMessage = parsedError.message || parsedError.error || errorMessage;
                                    } catch (e) {
                                        errorMessage = eventData || errorMessage;
                                    }
                                    callbacks.onError?.(errorMessage);
                                    break;
                            }
                        } else if (dataMatch) {
                            try {
                                const parsedData = JSON.parse(dataMatch[1]);
                                if (parsedData.text) {
                                    console.log("📝 接收到文本片段:", parsedData.text);
                                    fullResponse += parsedData.text;
                                    callbacks.onData?.(parsedData.text);
                                }
                            } catch (e) {
                                console.warn('⚠️ 解析数据错误:', e, dataMatch[1]);
                            }
                        }
                    }
                }
                
                console.log("\n🎊 AI生成完成");
                console.log("📋 最终完整响应:", fullResponse);
                
                return fullResponse;
                
            } catch (error) {
                console.error('💥 调用AI接口时发生错误:', error);
                
                if (error instanceof TypeError && error.message.includes('fetch')) {
                    const networkError = '🌐 网络连接错误，请检查服务器是否正在运行';
                    console.error(networkError);
                    callbacks.onError?.(networkError);
                    throw new Error(networkError);
                }
                
                callbacks.onError?.(error instanceof Error ? error.message : '未知错误');
                throw error;
            }
        }

        // 测试函数
        async function testBasicAI() {
            const prompt = "请介绍一下React框架的主要特点和优势，包括虚拟DOM、组件化、单向数据流等核心概念";
            await runTest(prompt, 'basic');
        }

        async function testJavaScript() {
            const prompt = "请用简洁的语言介绍JavaScript的主要特点和应用场景";
            await runTest(prompt, 'basic');
        }

        async function testTypeScript() {
            const prompt = "什么是TypeScript？它相比JavaScript有哪些优势？";
            await runTest(prompt, 'basic');
        }

        async function testCustomPrompt() {
            const prompt = document.getElementById('customPrompt').value.trim();
            if (!prompt) {
                updateStatus('customStatus', '❌ 请输入prompt', 'error');
                return;
            }
            await runTest(prompt, 'custom');
        }

        async function runTest(prompt, type) {
            const statusId = type + 'Status';
            const outputId = type + 'Output';
            const outputDiv = document.getElementById(outputId);
            
            updateStatus(statusId, '🚀 正在调用AI接口...', 'info');
            outputDiv.style.display = 'block';
            outputDiv.textContent = '';

            try {
                const result = await generateAi(prompt, {
                    onStart: () => {
                        outputDiv.textContent += '🤖 AI开始生成响应...\n\n';
                    },
                    onData: (text) => {
                        outputDiv.textContent += text;
                        outputDiv.scrollTop = outputDiv.scrollHeight;
                    },
                    onEnd: (fullResponse) => {
                        outputDiv.textContent += '\n\n✅ 响应完成！';
                        updateStatus(statusId, '✅ AI调用成功！请查看上方输出和控制台。', 'success');
                        
                        // 打印完整结果到控制台
                        console.log('\n' + '='.repeat(50));
                        console.log('🎯 最终完整响应:');
                        console.log('='.repeat(50));
                        console.log(fullResponse);
                        console.log('='.repeat(50));
                    },
                    onError: (error) => {
                        outputDiv.textContent += `\n\n❌ 错误: ${error}`;
                        updateStatus(statusId, `❌ 调用失败: ${error}`, 'error');
                    }
                });
            } catch (error) {
                outputDiv.textContent += `\n\n💥 异常: ${error.message}`;
                updateStatus(statusId, `💥 调用异常: ${error.message}`, 'error');
            }
        }

        async function checkConnection() {
            updateStatus('connectionStatus', '🔍 正在检查服务器连接...', 'info');
            
            try {
                const response = await fetch('http://localhost:3001/api/chatStream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ prompt: 'test connection' }),
                });
                
                if (response.ok) {
                    updateStatus('connectionStatus', '✅ 服务器连接正常，接口可用', 'success');
                    console.log('✅ 服务器连接测试成功');
                } else {
                    updateStatus('connectionStatus', `⚠️ 服务器响应异常: ${response.status} ${response.statusText}`, 'error');
                    console.log('⚠️ 服务器响应异常:', response.status, response.statusText);
                }
            } catch (error) {
                updateStatus('connectionStatus', `❌ 无法连接到服务器: ${error.message}`, 'error');
                console.log('❌ 服务器连接失败:', error.message);
            }
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 generateAi测试页面已加载');
            console.log('📖 可用的测试函数:');
            console.log('  - testBasicAI() - 测试React介绍');
            console.log('  - testJavaScript() - 测试JavaScript特点');
            console.log('  - testTypeScript() - 测试TypeScript介绍');
            console.log('  - testCustomPrompt() - 测试自定义prompt');
            console.log('  - checkConnection() - 检查服务器连接');
            console.log('  - generateAi(prompt, callbacks) - 直接调用AI函数');
            
            // 自动检查连接
            setTimeout(checkConnection, 1000);
        });
    </script>
</body>
</html>
